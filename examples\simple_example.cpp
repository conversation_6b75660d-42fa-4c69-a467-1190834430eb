#include "arc_common_logger.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    try {
        std::cout << "Arc Common Logger Example\n";
        std::cout << "========================\n\n";

        // Example 1: Using default logger with synchronous rotating sink
        std::cout << "1. Creating default logger with synchronous rotating sink...\n";
        arc::LoggerConfig sync_config;
        sync_config.log_file_path = "logs/sync_app.log";
        sync_config.max_file_size = 1024 * 1024;  // 1MB
        sync_config.max_files = 3;
        sync_config.async_mode = false;
        sync_config.log_level = spdlog::level::debug;

        auto& logger_manager = arc::ArcCommonLogger::getInstance();
        auto default_logger = logger_manager.initDefaultLogger(sync_config);

        // Test logging with default logger
        ARC_LOG_INFO("Application started");
        ARC_LOG_DEBUG("Debug message from default logger");
        ARC_LOG_WARN("Warning message with parameter: {}", 42);
        ARC_LOG_ERROR("Error message");

        std::cout << "   Logged messages to: " << sync_config.log_file_path << "\n\n";

        // Example 2: Creating a named logger with asynchronous rotating sink
        std::cout << "2. Creating named logger with asynchronous rotating sink...\n";
        arc::LoggerConfig async_config;
        async_config.log_file_path = "logs/async_module.log";
        async_config.max_file_size = 512 * 1024;  // 512KB
        async_config.max_files = 5;
        async_config.async_mode = true;
        async_config.log_level = spdlog::level::trace;
        async_config.async_queue_size = 4096;
        async_config.async_thread_count = 1;

        auto async_logger = logger_manager.getLogger("module_logger", async_config);

        // Test logging with named logger
        async_logger->info("Module logger initialized");
        async_logger->debug("Processing data...");
        async_logger->trace("Detailed trace information");

        std::cout << "   Logged messages to: " << async_config.log_file_path << "\n\n";

        // Example 3: Creating multiple logger instances
        std::cout << "3. Creating multiple logger instances...\n";

        // Database logger
        arc::LoggerConfig db_config;
        db_config.log_file_path = "logs/database.log";
        db_config.max_file_size = 2 * 1024 * 1024;  // 2MB
        db_config.max_files = 10;
        db_config.async_mode = false;
        db_config.log_level = spdlog::level::info;
        db_config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [DB] [%l] %v";

        auto db_logger = logger_manager.getLogger("database", db_config);

        // Network logger
        arc::LoggerConfig net_config;
        net_config.log_file_path = "logs/network.log";
        net_config.max_file_size = 1024 * 1024;  // 1MB
        net_config.max_files = 5;
        net_config.async_mode = true;
        net_config.log_level = spdlog::level::debug;
        net_config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [NET] [%l] [%t] %v";

        auto net_logger = logger_manager.getLogger("network", net_config);

        // Log some messages
        db_logger->info("Database connection established");
        db_logger->warn("Slow query detected: SELECT * FROM large_table");

        net_logger->debug("Incoming connection from *************");
        net_logger->info("Data packet received: {} bytes", 1024);

        std::cout << "   Database logs: " << db_config.log_file_path << "\n";
        std::cout << "   Network logs: " << net_config.log_file_path << "\n\n";

        // Example 4: Demonstrating concurrent logging
        std::cout << "4. Demonstrating concurrent logging...\n";

        auto concurrent_logger = logger_manager.getLogger("concurrent_test", async_config);

        // Create multiple threads that log concurrently
        std::vector<std::thread> threads;
        for (int i = 0; i < 5; ++i) {
            threads.emplace_back([&concurrent_logger, i]() {
                for (int j = 0; j < 10; ++j) {
                    concurrent_logger->info("Thread {} - Message {}", i, j);
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            });
        }

        // Wait for all threads to complete
        for (auto& t : threads) {
            t.join();
        }

        std::cout << "   Concurrent logging completed\n\n";

        // Example 5: Logger management operations
        std::cout << "5. Logger management operations...\n";

        // List all active loggers
        auto logger_names = logger_manager.getLoggerNames();
        std::cout << "   Active loggers (" << logger_names.size() << "):\n";
        for (const auto& name : logger_names) {
            std::cout << "     - " << name << "\n";
        }

        // Flush all loggers
        std::cout << "   Flushing all loggers...\n";
        logger_manager.flushAll();

        // Change global log level
        std::cout << "   Setting global log level to ERROR...\n";
        logger_manager.setGlobalLogLevel(spdlog::level::err);

        // Test that only error messages are logged now
        ARC_LOG_INFO("This info message should not appear");
        ARC_LOG_ERROR("This error message should appear");

        // Reset log level
        logger_manager.setGlobalLogLevel(spdlog::level::info);

        std::cout << "\n6. Example completed successfully!\n";
        std::cout << "   Check the 'logs/' directory for generated log files.\n";

        // Cleanup (optional - destructor will handle this)
        logger_manager.shutdown();

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}